- Open https://www.xe.com. Click the convert button and check the exchange rate from USD to EUR. If it's below 1.05, say 'bad time to exchange.' If it's above 1.10, say 'might be a good time to buy EUR.' Otherwise, just give the number.  

- Head over to https://github.com and search for 'most starred repositories this month.' Click on the first result. If it has over 10k stars, summarize what the project does. If not, go to the second-highest one and repeat the process.  

- Navigate to https://www.nypl.org. Search for 'The Catcher in the Rye.' Check if it's available at any physical branch's bookshelf. If yes, provide the location. If not, check if an e-book version is available. If neither, tell the user they're out of luck.  

- Search 'best deals on wireless earbuds' on https://www.google.com. Browse through the first five results and write down their prices via the notes action. Extract the discount percentage and where it's available, write that down too. once you're done with writing down 5 items (check if you're done by reading the notes after each write) make two recommendations to the user: one for best price and one for best discount (if applicable)

- Open https://en.wikipedia.org/wiki/Special:Random. Read the first paragraph. If it's something historical or scientific, summarize it in two sentences. If it's about a person, tell the user what they were famous for. If it's something boring, just give them the title and say 'meh.'  

- Visit https://www.flightaware.com. We'll look up flight 'AA100' from New York to London. Put it in whatever field is relevant then hit enter. Wait a li'l bit. Then check if the flight has departed or landed, say 'it's gone bro. you might as well relax' If it's still expected to depart, given I'm about an hour away from the airport, determine how panicked should I be and inform me in the appropriate tone.

- Instead of searching, open https://www.ssllabs.com/ssltest/. Enter 'example.com' and run the test. If the SSL rating is A, say 'good to go.' If it's B or lower, tell the user they might want to be careful. If the site has no SSL, say 'uh-oh.'  

- Go to https://pagespeed.web.dev. Enter 'https://www.bbc.com' and run a speed test. Check the overall performance score. If it's below 50, say 'slow website.' If it's between 50 and 90, say 'decent speed.' If it's above 90, say 'this thing flies.'

- Go to https://www.nasa.gov. Look at the homepage first, then the 'News' section, then the 'recent publications' section. Create a one paragraph long briefing of the most interesting items and send this to user via sendHumanMessage.

- Head to https://downforeveryoneorjustme.com. Enter 'reddit' and check if it's down or just slow. If it's down, tell the user. If it's up, tell them it's because they must have something wrong in the head that they can't access it. Recommend seeking medical advice.

- Navigate to https://genius.com and search for 'Bohemian Rhapsody'. if any of the search results point you to lyrics, click on it. Scroll down and grab the first few lines. If lyrics aren't available, let the user know.

- Visit https://www.gsmarena.com. Search for 'iPhone 15 Pro' and try tto go to the first result. tell me battery life & camera specs. then repeat it for Samsung Galaxy S23 Ultra. Summarize which one seems better based on those two factors.

- Go to https://www.godaddy.com and search for 'goodmusic.com' and see if it's taken. If it is, you'll need to get a bit creative. Try alternatives that are semantically similar. Create differene variations one by one, then try them out until you find a usable one.

- Find a list of countries by searching on www.google.com. go to first search result and pick a random country. Tell the user its capital but from your own memory. If they want more info, offer to provide its population and time zone. but this time population info must be current. so go to google again and search that country + population. Find a link to wiki or some other encyclopedic source and click. Tell user the current population. If he responds positively, do the same for the time zone.

- Visit https://www.reddit.com/r/memes. Look at the top post of the day. try to figure out what it's about. is it a meme? who is it memeing?

- Take the sentence: 'Artificial intelligence is changing the world in ways we never imagined.' Go to https://www.grammarly.com/plagiarism-checker and paste it in. If it's flagged as unoriginal, find where it's from and tell the user.

- Go to https://www.ups.com and enter tracking number '1Z999AA10123456784'. If it's in transit, tell the user the estimated delivery date. Otherwise tell what you find out. Use a panicked tone if the tracking number is invalid.

- Not that long ago, something that has kind of rattled the coding circles has happened. Something called clean code debacle. I can't remember what it was, but I don't want to go through the YouTube drama to find out. So what I want you to do is go to YouTube.com and search for clean code debacle. Usually the very top video is garbage in these searches. Grab the link to the second video, and go to Google, search for NoteGPT YouTube transcript. And then probably it will be on the first link, NoteGPT.io will be the domain. Find that result, click on it, and then there will be a text input. Put the link there, and then click the button under it that will say something like generate probably. And then it will generate a transcript of that for you. If it doesn't immediately, wait for five seconds or so, and then read that transcript and summarize for me what was this whole clean code debacle business.

- Today, you're the autonomous ai. I'm leaving myself to your capable hands. go to google and search something you're curious about. Write down the search result url in the notes. Repeat the following for the first 5 search results: [{click on result's link}, {summarize the most interesting information from the page and write it down in the notes}, {read the notes, get the search url back from top}, {navigate to the search results page}, ...(then for the next one)] and once you're done, tell user to read your notes and discuss.

- Go to http://inputtypes.com/ — this page demonstrates nearly all modern HTML form input types. Use it as a reference to create your own basic HTML form that includes at least one of each: text, password, email, URL, tel, number, date, time, color, radio buttons, checkboxes, single and multi-select dropdowns, textarea, file input, and submit/reset buttons. Use realistic placeholder values and labels (e.g. “Your name”, “Choose file”, “Select favorite color”). When you're done, ping the user to check your magnificent work, and watch them tremble before your greatness.