import { <PERSON><PERSON><PERSON>, <PERSON> } from "playwright";
import { Action, ActionSchema } from './core/actions/types.js';
// Ensure DOM extractors are registered
import './core/page/initialize.js';

// Re-export everything from the browser modules
export * from './core/browser/index.js';

// Re-export Action types for backward compatibility
export { Action, ActionSchema } from './core/actions/types.js';

// Graph context interface shared with your state machine.
export interface GraphContext {
  browser?: Browser;
  page?: Page;
  action?: Action;
  retries?: number;
  history: string[];
  actionHistory?: Action[];   // Added to track actions for redundancy detection
  actionFeedback?: string;    // Added to provide feedback to LLMs about repetitive actions
  startTime?: number;
  lastScreenshot?: string;
  userGoal?: string;  
  
  // Success tracking fields
  successfulActions?: string[];  // Track successful actions
  lastActionSuccess?: boolean;   // Was the last action successful?
  successCount?: number;         // Count of consecutive successes
  previousPageState?: any;       // Store previous page state for comparison
  milestones?: string[];         // Milestones based on goal
  recognizedMilestones?: string[]; // Milestones achieved
  
  // Page content fields
  pageContent?: string;         // Structured content from page
  pageSummary?: string;        // Summary of the page content for LLM context
  lastSelector?: string;        // Last selector that was attempted
  compressedHistory?: string[]; // Compressed version of action history
}

// All function implementations are now in the browser modules
// This file only contains interfaces and re-exports for backward compatibility
