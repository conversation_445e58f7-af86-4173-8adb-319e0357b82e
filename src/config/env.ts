import { z } from 'zod';
import dotenvFlow from 'dotenv-flow';
import logger from '../utils/logger.js';

// Load environment variables
dotenvFlow.config();

// Environment validation schema
const EnvSchema = z.object({
  // Node environment
  NODE_ENV: z.enum(['development', 'test', 'production']).default('development'),
  
  // LLM Configuration
  LLM_PROVIDER: z.enum(['ollama', 'gemini', 'openai']).default('ollama'),
  LLM_MODEL: z.string().optional(),
  
  // API Keys (conditional based on provider)
  GEMINI_API_KEY: z.string().optional(),
  OPENAI_API_KEY: z.string().optional(),
  OPENAI_BASE_URL: z.string().url().optional(),
  
  // Ollama Configuration
  OLLAMA_HOST: z.string().url().default('http://localhost:11434'),
  
  // Browser Configuration
  HEADLESS: z.string().transform(val => val !== 'false').default('false'),
  START_URL: z.string().url().default('https://www.duckduckgo.com/'),
  DATA_DIR: z.string().optional(),
  PLAYWRIGHT_BROWSERS_PATH: z.string().optional(),
  
  // Directories
  LOG_DIR: z.string().default('./logs'),
  SCREENSHOT_DIR: z.string().default('./screenshots'),
  
  // Other Configuration
  UNIVERSAL_SUBMIT_SELECTOR: z.string().default('enterKeyPress'),
  DEBUG_LEVEL: z.string().transform(val => parseInt(val, 10)).default('0'),
  USER_FUNCTIONS_PATH: z.string().optional(),
});

// Validate environment variables
function validateEnv(): z.infer<typeof EnvSchema> {
  try {
    const env = EnvSchema.parse(process.env);
    
    // Additional validation based on LLM provider
    if (env.LLM_PROVIDER === 'gemini' && !env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY is required when LLM_PROVIDER is "gemini"');
    }
    
    if (env.LLM_PROVIDER === 'openai' && !env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY is required when LLM_PROVIDER is "openai"');
    }
    
    logger.info('Environment variables validated successfully', {
      provider: env.LLM_PROVIDER,
      nodeEnv: env.NODE_ENV,
      headless: env.HEADLESS,
    });
    
    return env;
  } catch (error) {
    logger.error('Environment validation failed', { error });
    throw new Error(`Environment validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Export validated environment
export const env = validateEnv();
export type Env = z.infer<typeof EnvSchema>;
