import readline from 'readline';
import fs from 'fs';
import path from 'path';
import logger from '../../utils/logger.js';
import { getAgentState } from '../../utils/agentState.js';
import { listAvailableFunctions, isUserFunctionCall, isListFunctionsRequest, processFunctionCall } from '../user-functions/functionParser.js';
import { GraphContext } from '../shared/types.js';

// Create readline interface for user input
export function promptUser(question: string): Promise<string> {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      rl.close();
      resolve(answer);
    });
  });
}

// Helper function to process user function calls
export async function processUserFunctionCall(ctx: GraphContext, functionCall: string): Promise<string> {
  const expandedPrompt = await processFunctionCall(functionCall);
  if (expandedPrompt) {
    const actionChoice = await promptUser(
      "Function detected. Do you want to:\n" +
      "1. Replace current goal with this function\n" +
      "2. Prepend to current goal\n" +
      "Enter 1 or 2: "
    );
    if (actionChoice === "1") {
      ctx.userGoal = expandedPrompt;
      ctx.history.push(`User replaced goal with function: ${functionCall}`);
      logger.info('Replaced goal with user function', {
        original: functionCall,
        newGoalPreview: expandedPrompt.substring(0, 100) + (expandedPrompt.length > 100 ? '...' : '')
      });
    } else {
      ctx.userGoal = `${expandedPrompt}\n\nAdditional context: ${ctx.userGoal}`;
      ctx.history.push(`User prepended function to goal: ${functionCall}`);
      logger.info('Prepended user function to goal', {
        original: functionCall,
        newGoalPreview: ctx.userGoal.substring(0, 100) + (ctx.userGoal.length > 100 ? '...' : '')
      });
    }
    const { initializeMilestones } = await import('../automation/milestones.js');
    initializeMilestones(ctx);
    ctx.actionFeedback = `👤 FUNCTION ACTIVATED: ${functionCall} has been processed and goal updated.`;
  } else {
    ctx.actionFeedback = `👤 FUNCTION ERROR: Failed to process \"${functionCall}\". Please check syntax and function name.`;
    ctx.history.push(`Failed to process function call: \"${functionCall}\"`);
  }
  return "chooseAction";
}

// sendHumanMessageHandler
export async function sendHumanMessageHandler(ctx: GraphContext): Promise<string> {
  if (!ctx.page || !ctx.action) throw new Error("Invalid context");
  try {
    const beforeHelpState = {
      url: ctx.page.url(),
      title: await ctx.page.title()
    };
    const screenshotDir = process.env.SCREENSHOT_DIR || "./screenshots";
    const screenshotPath = path.join(screenshotDir, `human-help-${Date.now()}.png`);
    await fs.promises.mkdir(path.dirname(screenshotPath), { recursive: true });
    await ctx.page.screenshot({ path: screenshotPath });
    const question = ctx.action.question || 
      `I've tried ${ctx.retries} times but keep failing. The page title is \"${await ctx.page.title()}\". What should I try next?`;
    let pageInfo = "";
    try {
      pageInfo = await ctx.page.evaluate(() => {
        const mainContent = document.querySelector('main, article, #readme')?.textContent?.trim();
        return mainContent ? mainContent.substring(0, 2000) : document.title;
      });
    } catch (err) {
      logger.error("Failed to extract page context", err);
    }
    const formattedQuestion = `\nCurrent URL: ${ctx.page.url()}\nCurrent task: ${ctx.userGoal}\nRecent actions: ${ctx.history.slice(-3).join("\n")}\n\nAI needs your help (screenshot saved to ${screenshotPath}):\n${question}\n\nTip: You can use ::functions to list available function templates.\nYour guidance:`;
    logger.info("Asking for human help", {
      screenshot: screenshotPath,
      question,
      currentUrl: ctx.page.url(),
      task: ctx.userGoal
    });
    const humanResponse = await promptUser(formattedQuestion);
    if (isListFunctionsRequest(humanResponse)) {
      const functionsList = await listAvailableFunctions();
      logger.info('User requested function list');
      const followupPrompt = `\nAvailable User Functions:\n\n${functionsList}\n\nEnter a function call like ::functionName(\"arg1\") or new instructions:`;
      const followupResponse = await promptUser(followupPrompt);
      if (followupResponse.trim()) {
        if (isUserFunctionCall(followupResponse)) {
          return await processUserFunctionCall(ctx, followupResponse);
        } else {
          ctx.actionFeedback = `👤 HUMAN ASSISTANCE: ${followupResponse}`;
          ctx.history.push(`Human response: \"${followupResponse.substring(0, 50)}${followupResponse.length > 50 ? '...' : ''}\"`);
        }
      }
    }
    else if (isUserFunctionCall(humanResponse)) {
      return await processUserFunctionCall(ctx, humanResponse);
    }
    else {
      const newGoal = await promptUser("Do you want to update the goal? (Leave empty to keep current goal): ");
      if (newGoal.trim() !== "") {
        ctx.userGoal = newGoal;
        ctx.history.push(`User updated goal to: ${newGoal}`);
        const { initializeMilestones } = await import('../automation/milestones.js');
        initializeMilestones(ctx);
      }
      logger.info("Received human response", {
        responsePreview: humanResponse.substring(0, 100)
      });
      ctx.actionFeedback = `👤 HUMAN ASSISTANCE: ${humanResponse}`;
      ctx.history.push(`Asked human: \"${question.substring(0, 50)}${question.length > 50 ? '...' : ''}\"`);
      ctx.history.push(`Human response: \"${humanResponse.substring(0, 50)}${humanResponse.length > 50 ? '...' : ''}\"`);
    }
    const currentUrl = ctx.page.url();
    if (currentUrl !== beforeHelpState.url) {
      ctx.history.push(`Note: Page changed during human interaction from \"${beforeHelpState.url}\" to \"${currentUrl}\"`);
      ctx.retries = 0;
    }
    ctx.retries = 0;
    return "chooseAction";
  } catch (error) {
    logger.error("Human interaction failed", error);
    ctx.history.push(`Human interaction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return "handleFailure";
  }
}

export function createSendHumanMessageAction(question: string): {
  type: "sendHumanMessage";
  question: string;
  selectorType: "css" | "text" | "xpath";
  maxWait: number;
} {
  return {
    type: 'sendHumanMessage' as const,
    question,
    selectorType: 'css' as const,
    maxWait: 1000
  };
} 