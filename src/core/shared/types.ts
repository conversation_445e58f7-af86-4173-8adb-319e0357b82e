// src/core/shared/types.ts
import { Action, ActionResult } from '../actions/types.js';
import type { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';

// Re-export Action types for backward compatibility
export { Action, ActionResult } from '../actions/types.js';

export interface BrowserState {
  url: string;
  title: string;
  screenshot?: string;
  domHash: string;
  interactiveElements: string[];
}

export interface AgentContext {
  currentState: BrowserState;
  actionHistory: Array<{
    action: Action;
    result: ActionResult;
    timestamp: number;
  }>;
  llmSessionState: {
    model: string;
    temperature: number;
    retryCount: number;
  };
  recoveryState: {
    lastError: string;
    errorCount: number;
    fallbackTriggered: boolean;
  };
  persistence: {
    sessionId: string;
    storageKey: string;
    autoSaveInterval: number;
  };
}

export interface StateTransition {
  nextState: string;
  contextUpdate: Partial<AgentContext>;
}

export type StateHandler = (context: AgentContext) => Promise<StateTransition>;

export interface GraphContext {
  browser?: Browser;
  page?: Page;
  action?: Action;
  retries?: number;
  history: string[];
  actionHistory?: Action[];
  actionFeedback?: string;
  startTime?: number;
  lastScreenshot?: string;
  userGoal?: string;
  successfulActions?: string[];
  lastActionSuccess?: boolean;
  successCount?: number;
  previousPageState?: any;
  milestones?: string[];
  recognizedMilestones?: string[];
  pageContent?: string;
  pageSummary?: string;
  lastSelector?: string;
  compressedHistory?: string[];
}
