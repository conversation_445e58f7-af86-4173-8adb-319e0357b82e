import { <PERSON><PERSON><PERSON>, <PERSON> } from "playwright";
import logger from '../../utils/logger.js';
import { setOverlayStatus } from '../../utils/uiEffects.js';
import { PageAnalyzer } from '../page/analyzer.js';
import { ContentExtractor } from '../page/contentExtractor.js';
import { env } from '../../config/env.js';

/**
 * Create a new page and navigate to the starting URL
 */
export async function createPage(browser: Browser): Promise<Page> {
  logger.browser.action('createPage', {
    startUrl: env.START_URL
  });

  try {
    const page = await browser.newPage();
    
    // Set up navigation listener to re-add overlay on page changes
    page.on('load', async () => {
      try {
        // Small delay to ensure D<PERSON> is ready
        setTimeout(async () => {
          await setOverlayStatus(page, "Agent is observing page...");
        }, 500);
      } catch (err) {
        // Ignore errors here
      }
    });
    
    await page.goto(env.START_URL);
    
    // Initialize the overlay after the first page load
    await setOverlayStatus(page, "Agent is initialized and ready");
    
    logger.info('Page created and navigated to start URL', {
      url: await page.url(),
      title: await page.title()
    });
    
    return page;
  } catch (error) {
    logger.browser.error('createPage', error);
    throw error;
  }
}

/**
 * Get comprehensive page state including DOM analysis and content extraction
 */
export async function getPageState(page: Page): Promise<any> {
  logger.browser.action('getPageState', {
    url: page.url(),
    timestamp: Date.now()
  });

  try {
    // Test extractors to verify they're working
    const { testExtractors } = await import('../../utils/extractorTester.js');
    await testExtractors(page);
    
    // Get a standard snapshot with our new system
    const domSnapshot = await PageAnalyzer.extractSnapshot(page);
    
    // Extract content with progressive loading via ContentExtractor
    const { content, truncated, scrolled } = await ContentExtractor.extract(page);
    
    // Use pageInterpreter to return unified page content
    const { generatePageSummary } = await import('../../pageInterpreter.js');
    const pageSummary = await generatePageSummary(page, domSnapshot);
    
    logger.debug('Page state extracted', {
      url: page.url(),
      contentLength: content.length,
      truncated,
      scrolled,
      summaryLength: pageSummary.length
    });
    
    return {
      url: page.url(),
      title: await page.title(),
      content,
      pageSummary,
      domSnapshot,
      metadata: {
        truncated,
        scrolled,
        timestamp: Date.now()
      }
    };
  } catch (error) {
    logger.browser.error('getPageState', error);
    throw error;
  }
}

/**
 * Extract DOM snapshot using PageAnalyzer
 */
export async function extractDOMSnapshot(page: Page): Promise<any> {
  return PageAnalyzer.extractSnapshot(page);
}

/**
 * Text similarity utility function
 */
export function textSimilarity(a: string, b: string): number {
  return a === b ? 1 : 0;
}

/**
 * Compress history for better LLM context management
 */
export function compressHistory(history: string[]): string[] {
  if (history.length <= 10) return history;
  
  // Keep first 3 and last 7 entries, with a summary in between
  const start = history.slice(0, 3);
  const end = history.slice(-7);
  const middle = [`... (${history.length - 10} actions omitted) ...`];
  
  return [...start, ...middle, ...end];
}
