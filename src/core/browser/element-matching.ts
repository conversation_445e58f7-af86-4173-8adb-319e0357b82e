import { Page } from "playwright";
import logger from '../../utils/logger.js';

/**
 * Search the DOM for the best matching element based on a reference string
 */
export async function findBestMatch(
  page: Page,
  reference: string
): Promise<string | null> {
  logger.debug('Finding best match', {
    reference,
    currentUrl: page.url()
  });

  try {
    // First try exact match
    const exactMatch = await page.$(reference);
    if (exactMatch) {
      return reference;
    }

    // Common patterns and their alternatives
    const patternMatches: {[key: string]: string[]} = {
      'input[type=text]': [
        'textarea',
        '[role=searchbox]',
        '[role=search] input',
        'input[name=q]'
      ],
      'button[type=submit]': [
        '[role=button][aria-label*="search" i]',
        'button.search-button',
        'input[type=submit]',
        '[role=search] button'
      ]
    };

    // Check pattern matches first
    for (const [pattern, alternatives] of Object.entries(patternMatches)) {
      if (reference.includes(pattern)) {
        for (const alt of alternatives) {
          const exists = await page.$(alt);
          if (exists) {
            logger.debug('Found alternative selector', {
              original: reference,
              match: alt
            });
            return alt;
          }
        }
      }
    }

    // If no pattern match, try DOM search with minimal logging
    const bestElement = await page.evaluate((ref) => {
      const elements = document.querySelectorAll('*');
      let bestMatch = null;
      let bestScore = 0;

      for (const el of elements) {
        let score = 0;
        // Prioritize matching attributes
        if (el.id && el.id.includes(ref)) score += 3;
        if (el.getAttribute('name') === ref) score += 2;
        if (el.getAttribute('role') === ref) score += 2;
        if (el.textContent?.toLowerCase().includes(ref.toLowerCase())) score += 1;

        if (score > bestScore) {
          bestScore = score;
          bestMatch = {
            tag: el.tagName.toLowerCase(),
            id: el.id,
            name: el.getAttribute('name'),
            role: el.getAttribute('role')
          };
        }
      }
      return bestScore > 0 ? bestMatch : null;
    }, reference);

    if (bestElement) {
      // Build selector from best match
      const selector = buildSelectorFromMatch(bestElement);
      
      logger.debug('Found best element match', {
        original: reference,
        match: selector
      });
      
      return selector;
    }

    logger.debug('No match found', { reference });
    return null;
  } catch (error) {
    logger.error('Error finding best match', {
      error,
      reference,
      url: page.url()
    });
    return null;
  }
}

/**
 * Build a CSS selector from element match information
 */
function buildSelectorFromMatch(match: any): string {
  if (match.id) {
    return `#${match.id}`;
  }
  if (match.name) {
    return `${match.tag}[name="${match.name}"]`;
  }
  if (match.role) {
    return `${match.tag}[role="${match.role}"]`;
  }
  return match.tag;
}
