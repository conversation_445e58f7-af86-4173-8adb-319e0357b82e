import { chromium, Browser } from 'playwright';
import type { ChildProcess } from 'child_process';
import logger from '../../utils/logger.js';
import { env } from '../../config/env.js';

// Global Chrome process reference
let chromeProcess: ChildProcess | null = null;

export function getChromeProcess(): ChildProcess | null {
  return chromeProcess;
}

export const DEFAULT_NAVIGATION_TIMEOUT = 10000;
export const RETRY_DELAY_MS = 2000;
export const SIMILARITY_THRESHOLD = 0.7;

/**
 * Launch browser with Chrome DevTools Protocol connection
 */
export async function launchBrowser(): Promise<Browser> {
  logger.browser.action('launch', {
    userDataDir: env.DATA_DIR,
    executablePath: env.PLAYWRIGHT_BROWSERS_PATH,
    headless: env.HEADLESS,
  });

  try {
    if (!env.DATA_DIR || !env.PLAYWRIGHT_BROWSERS_PATH) {
      throw new Error(
        'DATA_DIR and PLAYWRIGHT_BROWSERS_PATH environment variables must be defined'
      );
    }

    const { spawn } = await import('child_process');
    const { existsSync } = await import('fs');

    // Verify paths exist
    if (!existsSync(env.DATA_DIR)) {
      throw new Error(`User data directory not found: ${env.DATA_DIR}`);
    }

    // Kill any existing Chrome processes to avoid lock conflicts
    try {
      const { execSync } = await import('child_process');
      execSync('taskkill /F /IM chrome.exe', { stdio: 'ignore' });
      // Give time for file locks to be released
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (e) {
      // Ignore if no processes were killed
    }

    // Launch Chrome with remote debugging enabled
    chromeProcess = spawn(
      env.PLAYWRIGHT_BROWSERS_PATH,
      [
        '--remote-debugging-port=9222',
        `--user-data-dir=${env.DATA_DIR}`,
        '--no-first-run',
        '--no-default-browser-check',
        '--disable-session-crashed-bubble',
        '--start-maximized',
        env.HEADLESS ? '--headless=new' : '',
      ].filter(Boolean),
      { detached: true, stdio: 'ignore' }
    );

    // Wait for Chrome to start properly by polling the DevTools endpoint
    await waitForChromeDevTools(30000);

    // Connect to Chrome via CDP
    const browser = await chromium.connectOverCDP('http://localhost:9222');

    // Clean up Chrome process when browser is disconnected
    browser.on('disconnected', () => {
      try {
        if (chromeProcess && !chromeProcess.killed) {
          chromeProcess.kill();
        }
      } catch (err) {
        logger.error('Failed to kill Chrome process', err);
      }
    });

    return browser;
  } catch (error) {
    logger.browser.error('launch', error);
    throw error;
  }
}

/**
 * Wait for Chrome DevTools to become available
 */
async function waitForChromeDevTools(timeoutMs = 30000): Promise<void> {
  const startTime = Date.now();
  const { default: fetch } = await import('node-fetch');

  logger.info('Waiting for Chrome DevTools to become available...');

  while (Date.now() - startTime < timeoutMs) {
    try {
      const response = await fetch('http://localhost:9222/json/version');
      if (response.ok) {
        logger.info('Chrome DevTools ready!');
        return;
      }
    } catch (e) {
      // Ignore errors during polling
    }

    // Wait a bit before trying again
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  throw new Error(`Chrome DevTools not available after ${timeoutMs}ms`);
}

/**
 * Simple retry utility function
 */
export async function doRetry<T>(
  fn: () => Promise<T>,
  retries: number = 2,
  delayMs: number = 1000
): Promise<T> {
  let attempt = 0;
  while (true) {
    try {
      return await fn();
    } catch (error) {
      if (++attempt > retries) {
        throw error;
      }
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }
}
