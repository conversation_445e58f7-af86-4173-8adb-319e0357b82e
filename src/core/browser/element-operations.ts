import { <PERSON>, <PERSON>ementH<PERSON><PERSON> } from 'playwright';
import { Action } from '../actions/types.js';
import { elementFinder } from '../elements/finder.js';
import logger from '../../utils/logger.js';

/**
 * Verify if an element exists on the page
 */
export async function verifyElementExists(
  page: Page,
  selector: string,
  selectorType: string = 'css'
): Promise<{ exists: boolean; count: number; suggestion: string | null }> {
  logger.browser.action('verifyElement', {
    selector,
    selectorType,
  });

  try {
    // Create a mock action to use with the elementFinder
    const mockAction: Action = {
      type: 'click',
      element: selector,
      selectorType: selectorType as 'css' | 'xpath' | 'text',
      maxWait: 2000,
    };

    // Try to find the element
    const element = await elementFinder.findElement(page, mockAction);

    if (element) {
      // Count elements
      let count = 1;
      try {
        if (selectorType === 'css') {
          count = await page.$$eval(selector, elements => elements.length);
        } else if (selectorType === 'xpath') {
          count = await page.$$eval(`xpath=${selector}`, elements => elements.length);
        } else {
          count = await page.$$eval(`text=${selector}`, elements => elements.length);
        }
      } catch (e) {
        // Keep count as 1 if we couldn't determine actual count
      }

      return {
        exists: true,
        count,
        suggestion: null,
      };
    }

    // Get alternative suggestions
    const alternatives = await elementFinder.getAlternativeSuggestions(page, selector);
    const suggestion = alternatives.length > 0 ? `Try instead: ${alternatives.join(', ')}` : null;

    return { exists: false, count: 0, suggestion };
  } catch (e) {
    logger.browser.error('verifyElement', {
      error: e,
      selector,
      selectorType,
    });
    logger.error('Error in verifyElementExists:', e);
    return { exists: false, count: 0, suggestion: null };
  }
}

/**
 * Get an element based on the action's selector with improved error handling
 */
export async function getElement(page: Page, action: Action): Promise<ElementHandle | null> {
  if (!action.element) {
    logger.error('Undefined selector provided for action', {
      actionType: action.type,
      action: JSON.stringify(action),
    });
    return null;
  }

  const selector = action.element;
  const selectorType = action.selectorType || 'css';
  const startTime = Date.now();

  try {
    logger.debug('Looking for element', {
      selector,
      selectorType,
      timeout: action.maxWait || 2000,
    });

    // Check if element exists using evaluate for better diagnostics
    const elementInfo = await page
      .evaluate(sel => {
        try {
          const elements = document.querySelectorAll(sel);
          if (elements.length === 0) return { found: false, count: 0 };

          const firstElement = elements[0] as HTMLElement;
          const style = window.getComputedStyle(firstElement);
          return {
            found: true,
            count: elements.length,
            isVisible: !!(
              style.display !== 'none' &&
              style.visibility !== 'hidden' &&
              (firstElement.offsetParent !== null || style.position === 'fixed')
            ),
            tagName: firstElement.tagName,
            text: firstElement.textContent?.substring(0, 100),
          };
        } catch (e) {
          return { found: false, error: String(e) };
        }
      }, selector)
      .catch(e => ({ found: false, error: String(e) }));

    logger.debug('Element search result', {
      selector,
      result: elementInfo,
      duration: `${Date.now() - startTime}ms`,
    });

    if (!elementInfo.found) {
      // If not found with regular selector, try alternative methods
      logger.warn(`Element not found with selector: ${selector}`, { elementInfo });

      // Check for partial matches or similar elements
      await captureCloseMatches(page, selector);
      return null;
    }

    // Element exists, but we still need to get a handle to it
    const elementHandle = await page.$(selector).catch(e => {
      logger.error(`Error getting handle for existing element: ${selector}`, { error: e });
      return null;
    });

    return elementHandle;
  } catch (error) {
    logger.error(`Error finding element: ${selector}`, {
      error,
      selector,
      url: page.url(),
    });
    return null;
  }
}

/**
 * Capture potential close matches for failed selectors
 */
async function captureCloseMatches(page: Page, failedSelector: string): Promise<void> {
  try {
    // For failed CSS selectors like #something or .something, try to find similar elements
    const generalizedSelector = failedSelector.startsWith('#')
      ? `[id*="${failedSelector.substring(1)}"]`
      : failedSelector.startsWith('.')
        ? `[class*="${failedSelector.substring(1)}"]`
        : null;

    if (generalizedSelector) {
      const similarElements = await page
        .evaluate(sel => {
          const elements = document.querySelectorAll(sel);
          return Array.from(elements)
            .slice(0, 5)
            .map(el => ({
              tagName: el.tagName,
              id: el.id || undefined,
              classes: Array.from(el.classList),
              text: el.textContent?.substring(0, 50),
            }));
        }, generalizedSelector)
        .catch(() => []);

      if (similarElements.length > 0) {
        logger.debug('Found similar elements', {
          originalSelector: failedSelector,
          generalizedSelector,
          similarElements,
        });
      }
    }

    // For any selector type, list visible interactive elements as guidance
    const visibleButtons = await page
      .evaluate(() => {
        return Array.from(document.querySelectorAll('button, [role="button"], a.btn'))
          .filter(el => {
            const rect = el.getBoundingClientRect();
            return rect.width > 0 && rect.height > 0;
          })
          .slice(0, 10)
          .map(el => ({
            tagName: el.tagName,
            id: el.id || undefined,
            classes: Array.from(el.classList),
            text: el.textContent?.trim().substring(0, 50) || undefined,
            selector: el.id
              ? `#${el.id}`
              : el.classList.length
                ? `.${Array.from(el.classList)[0]}`
                : el.tagName,
          }));
      })
      .catch(() => []);

    if (visibleButtons.length > 0) {
      logger.debug('Visible interactive elements on page that could be used instead', {
        visibleButtons,
      });
    }
  } catch (error) {
    logger.error('Error capturing close matches', { error });
  }
}
