import { Page } from "playwright";
import { Action } from '../actions/types.js';
import { getElement } from './element-operations.js';
import { doRetry } from './launcher.js';
import logger from '../../utils/logger.js';

/**
 * Enhanced verify action function with better diagnostics
 */
export async function verifyAction(page: Page, action: Action): Promise<boolean> {
  if (!action) {
    logger.error('Undefined action in verifyAction');
    return false;
  }
  
  const startTime = Date.now();
  let success = false;

  try {
    await doRetry(async () => {
      switch (action.type) {
        case "click": {
          if (!action.element) {
            logger.error('Undefined element for click action', { action });
            return false;
          }
          
          const elementExists = await page.$(action.element) !== null;
          if (!elementExists) {
            logger.debug('Element no longer exists after click (might be expected)', {
              selector: action.element
            });
            
            // If it's a button that caused navigation, this might be expected
            const urlChanged = page.url() !== action.previousUrl;
            if (urlChanged) {
              logger.debug('URL changed after click, considering successful', {
                from: action.previousUrl,
                to: page.url()
              });
              success = true;
              return true;
            }
          }
          
          // For clicks, we often don't have a great way to verify success other than checking
          // URL changes or DOM mutations. Log additional info to help diagnose.
          success = true;
          return true;
        }
        case "input": {
          if (!action.element) {
            logger.error('Undefined element for input action', { action });
            return false;
          }
          
          const element = await getElement(page, action);
          if (!element) return false;
          
          const value = await element.inputValue();
          success = value === action.value;
          
          if (!success) {
            logger.warn('Input verification failed', {
              expected: action.value,
              actual: value,
              selector: action.element
            });
          }
          
          return success;
        }
        case "navigate": {
          // For navigation, check if URL contains expected parts
          const currentUrl = page.url();
          
          if (action.value) {
            success = currentUrl.includes(action.value) || currentUrl === action.value;
          } else {
            // If no specific URL provided, just check that we're not on the same page
            success = currentUrl !== action.previousUrl;
          }
          
          if (!success) {
            logger.warn('Navigation verification failed', {
              expected: action.value,
              actual: currentUrl,
              previousUrl: action.previousUrl
            });
          }
          
          return success;
        }
        case "scroll": {
          // For scroll actions, we'll consider them successful if they complete without error
          // More sophisticated verification could check scroll position
          success = true;
          return true;
        }
        case "sendHumanMessage": // Always consider human interaction successful
          success = true;
          return true;
        case "wait":
          success = true;
          return true;
        case "notes": // Notes actions are verified separately by the notes handler
          success = true;
          return true;
        default:
          logger.warn(`Unknown action type: ${(action as any).type}`);
          return false;
      }
    }, 2);

    // Add completion INFO log
    logger.info(`Action verification ${success ? 'succeeded' : 'failed'}`, {
      type: action.type,
      element: action.element,
      duration: `${Date.now() - startTime}ms`
    });

    return success;
  } catch (error) {
    logger.browser.error('verifyAction', {
      error,
      action
    });
    logger.error("Error in verifyAction:", error);
    return false;
  }
}

/**
 * Analyze history patterns to detect repetitive actions
 */
export function analyzeHistoryPatterns(history: string[]): Array<{pattern: string, count: number, success: boolean}> {
  const patterns = new Map<string, {count: number, success: boolean}>();
  
  for (const item of history) {
    // Look for common action patterns
    let matched = false;
    
    // Click pattern
    const clickMatch = item.match(/Clicked (.+?)( successfully)?/);
    if (clickMatch) {
      const key = `clicking ${clickMatch[1]}`;
      const success = !!clickMatch[2];
      const current = patterns.get(key) || { count: 0, success };
      patterns.set(key, { count: current.count + 1, success });
      matched = true;
    }
    
    // Input pattern
    const inputMatch = item.match(/Input '(.+?)' to (.+)/);
    if (inputMatch) {
      const key = `inputting text to ${inputMatch[2]}`;
      const current = patterns.get(key) || { count: 0, success: true };
      patterns.set(key, { count: current.count + 1, success: current.success });
      matched = true;
    }
    
    // Navigation pattern
    const navMatch = item.match(/Navigated to: (.+)/);
    if (navMatch) {
      const key = `navigating`;
      const current = patterns.get(key) || { count: 0, success: true };
      patterns.set(key, { count: current.count + 1, success: current.success });
      matched = true;
    }
  }
  
  // Convert to array and sort by count
  return Array.from(patterns.entries())
    .map(([pattern, stats]) => ({ pattern, count: stats.count, success: stats.success }))
    .filter(p => p.count > 1) // Only return patterns that occurred more than once
    .sort((a, b) => b.count - a.count);
}
