import { z } from 'zod';

/**
 * Zod schema for action validation
 */
export const ActionSchema = z.object({
  type: z.enum(['click', 'input', 'navigate', 'wait', 'sendHumanMessage', 'notes', 'scroll']),
  element: z.string().optional(),
  value: z.string().optional(),
  description: z.string().optional(),
  selectorType: z.enum(['css', 'xpath', 'text']).optional().default('css'),
  maxWait: z.number().optional().default(2000),
  question: z.string().optional(),
  previousUrl: z.string().optional(),
  operation: z.enum(['add', 'read']).optional(),
  note: z.string().optional(),
  direction: z.enum(['up', 'down']).optional(),
});

/**
 * Represents an action to be performed in the browser
 */
export type Action = z.infer<typeof ActionSchema>;

/**
 * Result of an action execution
 */
export type ActionResult = 'success' | 'partial' | 'fail';

/**
 * Action history entry
 */
export interface ActionHistoryEntry {
  action: Action;
  result: ActionResult;
  timestamp: number;
}

/**
 * Zod schema for validation (used in browserExecutor.ts)
 * This is kept as a reference, but actual Zod import should be in the implementation file
 */
export const ActionSchemaReference = {
  type: ['click', 'input', 'navigate', 'wait', 'sendHumanMessage'],
  element: 'string?',
  value: 'string?',
  description: 'string?',
  selectorType: ['css', 'xpath', 'text'],
  maxWait: 'number',
  question: 'string?',
  previousUrl: 'string?',
};
