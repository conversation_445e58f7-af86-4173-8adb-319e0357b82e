# Technical Standards for TypeScript / Node.js Projects

These standards codify how we design, build, and maintain TypeScript back-ends, CLIs, tooling, or browser-automation agents.  
Adopt them at project kick-off and enforce them continuously via CI/CD.

---

## 1. Project Structure

```
project-root/
├── .github/            # Workflows, issue/PR templates
├── docs/               # Architectural & API docs (this file lives here)
├── src/
│   ├── core/           # Pure business logic – framework-agnostic
│   ├── adapters/       # DB, HTTP, LLM, browser, etc. (ports & adapters)
│   ├── features/       # Vertical slices grouping UI/API with use-cases
│   ├── utils/          # Generic helpers (≤ 400 LOC per file)
│   └── index.ts        # Composition root
├── tests/              # Jest / Vitest test files mirror `src/`
├── scripts/            # One-off maintenance scripts
├── .env.example        # Documented env vars
├── package.json
├── tsconfig.json
└── README.md
```

### Rules

1. **Single Source of Truth** – Runtime code only lives in `src/`.
2. **Bounded Contexts** – Do not import across `features/` without an explicit public API.
3. **File Size Limit** – 400 logical lines (≈ tokens) maximum; split otherwise.
4. **Index Barrel** – Each folder exposing code to others must export via an `index.ts`.
5. **No Cycles** – Circular dependencies are prohibited (enforced by `madge --circular`).

---

## 2. Dependency Management

| Area            | Standard                                                      | Example                      |
| --------------- | ------------------------------------------------------------- | ---------------------------- |
| Version pinning | Use exact versions (`"1.4.3"`) not ranges                     | `"jest": "30.1.0"`           |
| Lock file       | Commit `pnpm-lock.yaml`/`package-lock.json`                   | CI uses `--frozen-lockfile`  |
| Weight check    | Deny libraries > 200 kB minified unless justified             | ✔ `axios`, ✘ `moment`       |
| Layered imports | `src/core/*` may only import stdlib or other `core/*`         | Guards against infra leakage |
| Peer deps       | Expose adapters as peer/optional deps when building libraries | `react` for component libs   |

### Security & License

- Run `npm audit --production` on every PR.
- Block GPL-licensed transitive deps unless approved.
- Use Renovate or Dependabot; PRs must pass all tests.

---

## 3. Configuration & Environment

### `tsconfig.json`

```jsonc
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "strict": true,
    "rootDir": "src",
    "outDir": "dist",
    "types": ["node"],
    "esModuleInterop": false,
    "exactOptionalPropertyTypes": true
  },
  "include": ["src/**/*.ts"],
  "exclude": ["tests", "dist", "**/*.spec.ts"]
}
```

_Never_ edit generated `dist/` files; they are output-only.

### Environment Variables

1. Provide **`.env.example`** with every var, default, and comment.
2. Load vars with [`dotenv-flow`](https://github.com/avelino/dotenv-flow) or similar.
3. Validate at startup with `zod`/`envsafe`.

```ts
import { z } from 'zod';
const Env = z.object({
  NODE_ENV: z.enum(['development', 'test', 'production']),
  OPENAI_API_KEY: z.string().min(1),
});
Env.parse(process.env);
```

---

## 4. Documentation Requirements

| Doc                           | Purpose                           | Location |
| ----------------------------- | --------------------------------- | -------- |
| `README.md`                   | Quick-start, architecture diagram | root     |
| `docs/TECHNICAL_STANDARDS.md` | This rulebook                     | docs/    |
| `docs/architecture.md`        | Context, containers, components   | docs/    |
| `docs/api/openapi.yaml`       | REST/GraphQL schema               | docs/api |
| ADRs (`docs/adr/`)            | 1 ADR per significant decision    | docs/adr |

All diagrams use [PlantUML](https://plantuml.com) (`.puml`).

---

## 5. Testing & Quality Gates

| Layer      | Tool                                                | Requirement              |
| ---------- | --------------------------------------------------- | ------------------------ |
| Unit       | Jest/Vitest                                         | ≥ 80 % coverage          |
| E2E        | Playwright                                          | Critical paths only      |
| Static     | ESLint (`eslint:recommended`, `@typescript-eslint`) | 0 errors                 |
| Formatting | Prettier                                            | Enforced in CI           |
| Commit Msg | Conventional Commits                                | Checked via `commitlint` |

CI stages:

1. **lint** → **type-check** → **test** → **build**  
   Fail fast; artifacts only on success.

---

## 6. Commit & Branch Strategy

- Default branch: `main`
- Feature branches: `feat/<scope>`; bugfix: `fix/<issue>`.
- Pull Requests require:
  1. Passing CI
  2. At least one approving review
  3. Linked issue or ADR reference
- Squash & merge; use commit message template:

  ```
  feat(core): short summary

  * Motivation
  * High-level design
  ```

---

## 7. Release & Versioning

- Semantic Versioning (`MAJOR.MINOR.PATCH`)
- Use `changesets` or `semantic-release`.
- Changelog auto-generated; manual edits allowed only in "Unreleased" section.

---

## 8. Checklist (Pre-merge)

| ✓                                                     | Item |
| ----------------------------------------------------- | ---- |
| ☐ `npm run lint` passes without errors                |
| ☐ `npm run test` passes; coverage ≥ threshold         |
| ☐ No TODO/FIXME left in changed lines                 |
| ☐ Documentation updated (`README`, ADRs)              |
| ☐ All public APIs are exported via `index.ts` barrels |
| ☐ `.env.example` lists new env vars                   |
| ☐ No large binaries/artifacts committed               |
| ☐ Code owners / reviewers approved                    |

---

## 9. Example: Adding a New LLM Provider

1. Create `src/adapters/llm/<ProviderName>Processor.ts` implementing `ILLMProcessor`.
2. Register in `src/adapters/llm/index.ts`.
3. Add provider specific env vars to `.env.example`.
4. Write unit tests mocking HTTP calls.
5. Add integration test to `tests/adapters/llm/<provider>.spec.ts`.
6. Update documentation (`docs/adr/NNN-add-<provider>-llm.md`).

---

## 10. Enforcement

- **Pre-commit hooks** (`husky`): lint, type-check, format.
- **CI**: blocks merge if any rule is violated.
- **Code Owners**: critical paths (security, infra) require owner review.
- **Periodic Audits**: quarterly checklist run across projects.

---

Adhering to these standards ensures maintainable, secure, and scalable TypeScript projects while enabling efficient collaboration among developers and AI tooling.
