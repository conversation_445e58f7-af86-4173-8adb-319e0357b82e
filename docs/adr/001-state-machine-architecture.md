# ADR-001: State Machine Architecture for Browser Automation

## Status
Accepted

## Context
The browser automation system needs to manage complex workflows involving multiple steps, error handling, and decision points. Traditional linear scripting approaches become difficult to maintain and debug as complexity grows.

## Decision
We will implement a state machine architecture to manage the automation workflow, where:

1. Each automation phase is represented as a discrete state
2. States are responsible for specific automation tasks
3. Context is passed between states to maintain session data
4. State transitions are based on action results and conditions

## Consequences

### Positive
- **Maintainability**: Clear separation of concerns between different automation phases
- **Debuggability**: Easy to trace execution flow and identify where issues occur
- **Extensibility**: New states can be added without affecting existing ones
- **Error Handling**: Failed states can transition to recovery states
- **Testing**: Individual states can be unit tested in isolation

### Negative
- **Complexity**: Initial setup requires more boilerplate than linear scripts
- **Learning Curve**: Team needs to understand state machine concepts
- **Overhead**: Small automations may be over-engineered

## Implementation
- State handlers registered in `src/core/automation/machine.ts`
- Context object passed between states contains browser state, history, and metrics
- State transitions determined by return values from state handlers
- Recovery and error states handle failure scenarios

## Alternatives Considered
1. **Linear Scripting**: Simpler but harder to maintain for complex workflows
2. **Event-Driven Architecture**: More complex and harder to reason about execution flow
3. **Pipeline Pattern**: Good for data transformation but less suitable for interactive workflows
