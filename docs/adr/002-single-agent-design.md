# ADR-002: Single Agent Design Pattern

## Status

Accepted

## Context

Multi-agent systems are popular in AI applications, but they introduce complexity around coordination, communication, and potential conflicts between agents. For browser automation, we need to decide between a single intelligent agent or multiple specialized agents.

## Decision

We will implement a single agent design where one AI agent handles all automation decisions, supported by specialized modules for different capabilities.

## Rationale

1. **Simplicity**: No inter-agent communication protocols needed
2. **Consistency**: Single context and decision-making process
3. **Reliability**: No coordination failures between agents
4. **Efficiency**: Lower computational overhead
5. **Debugging**: Easier to trace decisions and actions

## Consequences

### Positive

- **Reduced Complexity**: No agent coordination logic required
- **Better Context**: Single agent maintains full context throughout session
- **Predictable Behavior**: Consistent decision-making process
- **Easier Testing**: Single agent behavior is easier to test and validate
- **Lower Resource Usage**: Only one LLM instance needed

### Negative

- **Potential Bottleneck**: Single agent must handle all decision types
- **Less Specialization**: Cannot have highly specialized agents for specific tasks
- **Scaling Limitations**: May not scale to very complex multi-domain tasks

## Implementation

- Single LLM processor handles all automation decisions
- Specialized modules (page analysis, element finding, etc.) provide capabilities
- State machine coordinates different automation phases
- Context object maintains shared state across all operations

## Alternatives Considered

1. **Multi-Agent System**: More complex coordination but potentially more specialized
2. **Hierarchical Agents**: Master agent coordinating sub-agents
3. **Swarm Intelligence**: Multiple simple agents working together
