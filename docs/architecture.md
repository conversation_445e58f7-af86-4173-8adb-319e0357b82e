# Architecture

This document describes the context, containers, and components of the Agentic AI Browser system using the C4 model.

## Context Diagram

The Agentic AI Browser operates within the following context:

```plantuml
@startuml context
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Context.puml

Person(user, "User", "Provides goals and interacts with the browser automation system")
System(browser_agent, "Agentic AI Browser", "Intelligent web automation agent that navigates websites and performs tasks")

System_Ext(llm_providers, "LLM Providers", "External AI services (OpenAI, Gemini, Ollama)")
System_Ext(web_browsers, "Web Browsers", "Chrome/Chromium browsers controlled via Playwright")
System_Ext(websites, "Target Websites", "Web applications and sites to be automated")

Rel(user, browser_agent, "Provides automation goals")
<PERSON>l(browser_agent, llm_providers, "Requests AI decisions and actions")
Rel(browser_agent, web_browsers, "Controls browser via Playwright")
Rel(web_browsers, websites, "Navigates and interacts with")
@enduml
```

## Container Diagram

The system consists of the following containers:

```plantuml
@startuml containers
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

Person(user, "User")

System_Boundary(browser_agent, "Agentic AI Browser") {
    Container(automation_engine, "Automation Engine", "Node.js/TypeScript", "Orchestrates browser automation using state machines")
    Container(llm_adapters, "LLM Adapters", "Node.js/TypeScript", "Interfaces with various LLM providers")
    Container(browser_executor, "Browser Executor", "Node.js/TypeScript", "Controls browser via Playwright API")
    Container(page_analyzer, "Page Analyzer", "Node.js/TypeScript", "Extracts and analyzes DOM content")
    ContainerDb(success_patterns, "Success Patterns", "JSON Files", "Stores successful interaction patterns by domain")
    ContainerDb(logs, "Logs & Notes", "File System", "Session logs, screenshots, and user notes")
}

System_Ext(llm_providers, "LLM Providers")
System_Ext(browsers, "Browsers")

Rel(user, automation_engine, "Provides goals")
Rel(automation_engine, llm_adapters, "Requests decisions")
Rel(automation_engine, browser_executor, "Executes actions")
Rel(browser_executor, page_analyzer, "Requests page analysis")
Rel(automation_engine, success_patterns, "Reads/writes patterns")
Rel(automation_engine, logs, "Writes logs/notes")
Rel(llm_adapters, llm_providers, "API calls")
Rel(browser_executor, browsers, "Playwright API")
@enduml
```

## Component Diagram - Core Module

The core module contains the main business logic:

```plantuml
@startuml core_components
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

Container_Boundary(core, "Core Module") {
    Component(state_machine, "State Machine", "Manages automation workflow states")
    Component(context_manager, "Context Manager", "Maintains shared context across states")
    Component(action_extractor, "Action Extractor", "Extracts actions from LLM responses")
    Component(action_validator, "Action Validator", "Validates actions against current state")
    Component(element_finder, "Element Finder", "Locates DOM elements using various strategies")
    Component(progress_tracker, "Progress Tracker", "Monitors automation progress and detects issues")
    Component(milestone_checker, "Milestone Checker", "Tracks goal completion milestones")
}

Container_Boundary(adapters, "Adapters") {
    Component(llm_processor, "LLM Processor", "Abstract interface for LLM communication")
    Component(browser_adapter, "Browser Adapter", "Playwright browser control interface")
}

Rel(state_machine, context_manager, "Updates context")
Rel(state_machine, action_extractor, "Processes LLM responses")
Rel(action_extractor, action_validator, "Validates extracted actions")
Rel(action_validator, element_finder, "Verifies element existence")
Rel(state_machine, progress_tracker, "Reports progress")
Rel(state_machine, milestone_checker, "Checks milestones")
Rel(state_machine, llm_processor, "Requests decisions")
Rel(state_machine, browser_adapter, "Executes browser actions")
@enduml
```

## Key Architectural Principles

### 1. Hexagonal Architecture (Ports & Adapters)

- **Core**: Pure business logic in `src/core/` - framework agnostic
- **Adapters**: External integrations in `src/adapters/` (LLM providers, browsers)
- **Features**: Vertical slices in `src/features/` grouping related functionality

### 2. State Machine Pattern

- Automation workflow managed as a state machine
- Each state handles specific automation phases
- Context passed between states maintains session data

### 3. Strategy Pattern for Element Location

- Multiple strategies for finding DOM elements
- Fallback mechanisms for robust element selection
- Domain-specific pattern learning and reuse

### 4. Single Responsibility Principle

- Each module has a focused responsibility
- Clear separation between concerns
- Maximum file size of 400 lines enforced

### 5. Dependency Inversion

- Core modules depend on abstractions, not implementations
- LLM providers are interchangeable via common interface
- Browser automation abstracted from specific implementations

## Data Flow

1. **User Input**: User provides automation goal
2. **State Initialization**: System initializes browser and context
3. **Page Analysis**: DOM content extracted and analyzed
4. **LLM Decision**: Current state sent to LLM for next action
5. **Action Extraction**: LLM response parsed into structured actions
6. **Action Validation**: Actions validated against current page state
7. **Element Location**: Target elements found using multiple strategies
8. **Action Execution**: Browser actions performed via Playwright
9. **Progress Tracking**: Success/failure tracked and patterns stored
10. **State Transition**: Move to next state based on results

## Error Handling & Recovery

- **Graceful Degradation**: System continues with reduced functionality on errors
- **Retry Mechanisms**: Failed actions retried with different strategies
- **Pattern Learning**: Successful interactions stored for future use
- **Recovery Engine**: Automatic recovery from common failure scenarios
- **Comprehensive Logging**: All actions and decisions logged for debugging
