# Dependencies
node_modules/
npm-debug.log

# Build output
dist/
.build/

# Test files
coverage/
.nyc_output/

# IDE files
.vscode/
.idea/
*.iml
.history/
logs/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# System files
.DS_Store
Thumbs.db

# Screenshots
screenshots/
data/
notes/

# Temporary files
tmp/
chrome-profile/
user-data/
*.tmp
*.bak

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache