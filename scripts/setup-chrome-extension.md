# Chrome Extension Setup Guide

This guide will help you install the AI Automation Helper Chrome extension to enable smooth browser automation.

## 🚀 Quick Setup

### Step 1: Enable Developer Mode in Chrome

1. Open Chrome and go to `chrome://extensions/`
2. Toggle **"Developer mode"** in the top-right corner
3. You should see additional options appear

### Step 2: Load the Extension

1. Click **"Load unpacked"** button
2. Navigate to your project folder
3. Select the `chrome-extension` folder
4. Click **"Select Folder"**

### Step 3: Verify Installation

1. You should see "AI Automation Helper" in your extensions list
2. The extension icon (🤖) should appear in your Chrome toolbar
3. Click the icon to open the popup and verify it's working

### Step 4: Configure Extension

1. Click the extension icon in the toolbar
2. Click **"Enable Automation Mode"**
3. Click **"Bypass Security Warnings"** if needed
4. The status should show "Enabled" for both options

## 🔧 What This Extension Does

### Automation Detection Bypass
- Hides the `navigator.webdriver` property
- Blocks common automation detection scripts
- Makes Chrome appear as a regular user browser

### Enhanced Permissions
- Provides access to all websites
- Enables advanced scripting capabilities
- Allows bypassing security restrictions

### Human-like Behavior
- Adds realistic mouse movement simulation
- Implements human-like timing variations
- Provides helper functions for natural interactions

## 🛠️ Troubleshooting

### Extension Not Loading
- Make sure you selected the `chrome-extension` folder (not a file)
- Check that all extension files are present
- Try refreshing the extensions page

### Automation Still Detected
- Make sure the extension is enabled
- Click "Enable Automation Mode" in the extension popup
- Restart Chrome and try again

### Permission Errors
- Click "Bypass Security Warnings" in the extension popup
- Make sure the extension has all required permissions
- Check Chrome's site settings for the target website

## 📋 Extension Files

The extension consists of these files:
- `manifest.json` - Extension configuration
- `background.js` - Background service worker
- `content.js` - Content script for all pages
- `injected.js` - Deep injection script
- `popup.html` - Extension popup interface
- `popup.js` - Popup functionality

## 🔒 Security Notes

This extension:
- ✅ Only runs on websites you visit
- ✅ Helps automation work smoothly
- ✅ Does not collect or transmit data
- ✅ Is open source and auditable

⚠️ **Important**: This extension disables some security features to enable automation. Only use it for legitimate automation purposes.

## 🎯 Usage with AI Browser

Once installed, the extension will automatically:
1. Be loaded when Chrome starts via the automation script
2. Enable automation-friendly settings
3. Bypass common detection methods
4. Provide helper functions for smooth automation

No additional configuration is needed - just install the extension and run your automation!

## 📞 Support

If you encounter issues:
1. Check the Chrome console for error messages
2. Verify all extension files are present
3. Try disabling other extensions temporarily
4. Restart Chrome completely

The extension should make your AI browser automation work much more reliably on all websites!
