// Popup script for AI automation helper
document.addEventListener('DOMContentLoaded', async () => {
  console.log('AI Automation Helper: Popup loaded');
  
  // Get DOM elements
  const extensionStatus = document.getElementById('extensionStatus');
  const automationStatus = document.getElementById('automationStatus');
  const securityStatus = document.getElementById('securityStatus');
  const tabCount = document.getElementById('tabCount');
  
  const enableAutomationBtn = document.getElementById('enableAutomation');
  const bypassSecurityBtn = document.getElementById('bypassSecurity');
  const refreshStatusBtn = document.getElementById('refreshStatus');
  
  // Update status display
  async function updateStatus() {
    try {
      // Get stored settings
      const settings = await chrome.storage.local.get([
        'automationEnabled',
        'securityBypassEnabled'
      ]);
      
      // Update automation status
      if (settings.automationEnabled) {
        automationStatus.textContent = 'Enabled';
        automationStatus.className = 'status-value status-enabled';
        enableAutomationBtn.textContent = 'Disable Automation Mode';
      } else {
        automationStatus.textContent = 'Disabled';
        automationStatus.className = 'status-value status-disabled';
        enableAutomationBtn.textContent = 'Enable Automation Mode';
      }
      
      // Update security status
      if (settings.securityBypassEnabled) {
        securityStatus.textContent = 'Enabled';
        securityStatus.className = 'status-value status-enabled';
        bypassSecurityBtn.textContent = 'Disable Security Bypass';
      } else {
        securityStatus.textContent = 'Disabled';
        securityStatus.className = 'status-value status-disabled';
        bypassSecurityBtn.textContent = 'Bypass Security Warnings';
      }
      
      // Get tab count
      const tabs = await chrome.tabs.query({});
      tabCount.textContent = tabs.length;
      
    } catch (error) {
      console.error('AI Automation Helper: Status update failed', error);
    }
  }
  
  // Enable/disable automation mode
  enableAutomationBtn.addEventListener('click', async () => {
    try {
      const settings = await chrome.storage.local.get(['automationEnabled']);
      const newState = !settings.automationEnabled;
      
      await chrome.storage.local.set({ automationEnabled: newState });
      
      if (newState) {
        // Enable automation on all tabs
        const tabs = await chrome.tabs.query({});
        
        for (const tab of tabs) {
          try {
            await chrome.scripting.executeScript({
              target: { tabId: tab.id },
              func: () => {
                window.__AUTOMATION_ENABLED__ = true;
                
                // Disable automation detection
                Object.defineProperty(navigator, 'webdriver', {
                  get: () => undefined,
                  configurable: true
                });
                
                console.log('AI Automation Helper: Automation enabled on tab');
              }
            });
          } catch (e) {
            // Ignore errors for restricted tabs
          }
        }
        
        showNotification('Automation mode enabled on all tabs', 'success');
      } else {
        showNotification('Automation mode disabled', 'info');
      }
      
      await updateStatus();
    } catch (error) {
      console.error('AI Automation Helper: Toggle automation failed', error);
      showNotification('Failed to toggle automation mode', 'error');
    }
  });
  
  // Enable/disable security bypass
  bypassSecurityBtn.addEventListener('click', async () => {
    try {
      const settings = await chrome.storage.local.get(['securityBypassEnabled']);
      const newState = !settings.securityBypassEnabled;
      
      await chrome.storage.local.set({ securityBypassEnabled: newState });
      
      if (newState) {
        showNotification('Security bypass enabled', 'success');
      } else {
        showNotification('Security bypass disabled', 'info');
      }
      
      await updateStatus();
    } catch (error) {
      console.error('AI Automation Helper: Toggle security bypass failed', error);
      showNotification('Failed to toggle security bypass', 'error');
    }
  });
  
  // Refresh status
  refreshStatusBtn.addEventListener('click', async () => {
    await updateStatus();
    showNotification('Status refreshed', 'info');
  });
  
  // Show notification
  function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 10px;
      left: 50%;
      transform: translateX(-50%);
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      z-index: 1000;
      transition: opacity 0.3s;
    `;
    
    // Set notification style based on type
    switch (type) {
      case 'success':
        notification.style.background = '#e8f5e8';
        notification.style.color = '#137333';
        break;
      case 'error':
        notification.style.background = '#fce8e6';
        notification.style.color = '#d93025';
        break;
      default:
        notification.style.background = '#e8f0fe';
        notification.style.color = '#1557b0';
    }
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }
  
  // Initial status update
  await updateStatus();
  
  console.log('AI Automation Helper: Popup initialization complete');
});
