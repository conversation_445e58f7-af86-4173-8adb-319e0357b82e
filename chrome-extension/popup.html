<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      line-height: 1.4;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .logo {
      font-size: 24px;
      margin-bottom: 5px;
    }
    
    .title {
      font-weight: bold;
      color: #1a73e8;
      margin-bottom: 5px;
    }
    
    .subtitle {
      color: #666;
      font-size: 12px;
    }
    
    .status {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;
    }
    
    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
    
    .status-item:last-child {
      margin-bottom: 0;
    }
    
    .status-label {
      font-weight: 500;
    }
    
    .status-value {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 12px;
      font-weight: 500;
    }
    
    .status-enabled {
      background: #e8f5e8;
      color: #137333;
    }
    
    .status-disabled {
      background: #fce8e6;
      color: #d93025;
    }
    
    .actions {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    
    .btn {
      padding: 10px 16px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .btn-primary {
      background: #1a73e8;
      color: white;
    }
    
    .btn-primary:hover {
      background: #1557b0;
    }
    
    .btn-secondary {
      background: #f8f9fa;
      color: #3c4043;
      border: 1px solid #dadce0;
    }
    
    .btn-secondary:hover {
      background: #f1f3f4;
    }
    
    .info {
      background: #e8f0fe;
      border-radius: 6px;
      padding: 12px;
      font-size: 12px;
      color: #1557b0;
      margin-top: 15px;
    }
    
    .version {
      text-align: center;
      color: #666;
      font-size: 11px;
      margin-top: 15px;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo">🤖</div>
    <div class="title">AI Automation Helper</div>
    <div class="subtitle">Chrome Extension for Browser Automation</div>
  </div>
  
  <div class="status">
    <div class="status-item">
      <span class="status-label">Extension Status</span>
      <span id="extensionStatus" class="status-value status-enabled">Active</span>
    </div>
    <div class="status-item">
      <span class="status-label">Automation Mode</span>
      <span id="automationStatus" class="status-value status-disabled">Disabled</span>
    </div>
    <div class="status-item">
      <span class="status-label">Security Bypass</span>
      <span id="securityStatus" class="status-value status-disabled">Disabled</span>
    </div>
    <div class="status-item">
      <span class="status-label">Active Tabs</span>
      <span id="tabCount" class="status-value status-enabled">0</span>
    </div>
  </div>
  
  <div class="actions">
    <button id="enableAutomation" class="btn btn-primary">Enable Automation Mode</button>
    <button id="bypassSecurity" class="btn btn-secondary">Bypass Security Warnings</button>
    <button id="refreshStatus" class="btn btn-secondary">Refresh Status</button>
  </div>
  
  <div class="info">
    <strong>How to use:</strong><br>
    1. Click "Enable Automation Mode" before running your AI automation<br>
    2. Use "Bypass Security Warnings" if you encounter permission issues<br>
    3. The extension will help your automation run smoothly
  </div>
  
  <div class="version">
    Version 1.0.0 | Agentic AI Browser
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
