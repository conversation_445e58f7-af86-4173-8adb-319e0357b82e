// Injected script for deep automation support
(function() {
  'use strict';
  
  console.log('AI Automation Helper: Injected script loaded');
  
  // Advanced automation detection bypass
  const originalDescriptor = Object.getOwnPropertyDescriptor(Navigator.prototype, 'webdriver');
  if (originalDescriptor) {
    Object.defineProperty(Navigator.prototype, 'webdriver', {
      get: () => undefined,
      configurable: true,
      enumerable: true
    });
  }
  
  // Block common detection methods
  const detectionMethods = [
    'webdriver',
    '__webdriver_script_fn',
    '__selenium_unwrapped',
    '__webdriver_unwrapped',
    '__driver_evaluate',
    '__webdriver_evaluate',
    '__selenium_evaluate',
    '__fxdriver_evaluate',
    '__driver_unwrapped',
    '__fxdriver_unwrapped',
    '_Selenium_IDE_Recorder',
    '_selenium',
    'calledSelenium',
    '$cdc_asdjflasutopfhvcZLmcfl_',
    '$chrome_asyncScriptInfo',
    '__$webdriverAsyncExecutor'
  ];
  
  detectionMethods.forEach(method => {
    if (window[method]) {
      delete window[method];
    }
    
    Object.defineProperty(window, method, {
      get: () => undefined,
      set: () => {},
      configurable: true,
      enumerable: false
    });
  });
  
  // Override document.documentElement.getAttribute to hide automation
  const originalGetAttribute = document.documentElement.getAttribute;
  document.documentElement.getAttribute = function(name) {
    if (name === 'webdriver' || name === 'selenium' || name === 'driver') {
      return null;
    }
    return originalGetAttribute.call(this, name);
  };
  
  // Override window.external to hide automation
  if (window.external && window.external.toString && window.external.toString().indexOf('Sequentum') !== -1) {
    window.external = undefined;
  }
  
  // Override screen properties to look more human
  Object.defineProperties(screen, {
    availTop: { value: 0 },
    availLeft: { value: 0 },
    availWidth: { value: screen.width },
    availHeight: { value: screen.height - 40 } // Account for taskbar
  });
  
  // Override navigator properties
  Object.defineProperties(navigator, {
    platform: { value: 'MacIntel', writable: false },
    productSub: { value: '********', writable: false },
    vendor: { value: 'Google Inc.', writable: false },
    vendorSub: { value: '', writable: false }
  });
  
  // Add realistic mouse movement tracking
  let mouseMovements = [];
  document.addEventListener('mousemove', (e) => {
    mouseMovements.push({ x: e.clientX, y: e.clientY, time: Date.now() });
    if (mouseMovements.length > 100) {
      mouseMovements = mouseMovements.slice(-50);
    }
  });
  
  // Add realistic timing variations
  const originalSetTimeout = window.setTimeout;
  window.setTimeout = function(callback, delay, ...args) {
    // Add small random variation to make timing more human-like
    const variation = Math.random() * 10 - 5; // ±5ms variation
    return originalSetTimeout.call(this, callback, delay + variation, ...args);
  };
  
  // Override performance.now to add slight variations
  const originalPerformanceNow = performance.now;
  let performanceOffset = 0;
  performance.now = function() {
    performanceOffset += Math.random() * 0.1 - 0.05; // Small drift
    return originalPerformanceNow.call(this) + performanceOffset;
  };
  
  // Add human-like behavior simulation
  window.__humanBehavior__ = {
    // Simulate human reading time
    calculateReadingTime: function(text) {
      const wordsPerMinute = 200 + Math.random() * 100; // 200-300 WPM
      const words = text.split(/\s+/).length;
      return (words / wordsPerMinute) * 60 * 1000; // Convert to milliseconds
    },
    
    // Simulate human reaction time
    getReactionTime: function() {
      return 150 + Math.random() * 300; // 150-450ms reaction time
    },
    
    // Simulate mouse movement to element
    simulateMouseMovement: function(element) {
      const rect = element.getBoundingClientRect();
      const targetX = rect.left + rect.width / 2;
      const targetY = rect.top + rect.height / 2;
      
      // Simulate curved mouse movement
      const steps = 10 + Math.random() * 10;
      const currentX = mouseMovements.length > 0 ? mouseMovements[mouseMovements.length - 1].x : 0;
      const currentY = mouseMovements.length > 0 ? mouseMovements[mouseMovements.length - 1].y : 0;
      
      for (let i = 0; i <= steps; i++) {
        const progress = i / steps;
        const x = currentX + (targetX - currentX) * progress + (Math.random() - 0.5) * 5;
        const y = currentY + (targetY - currentY) * progress + (Math.random() - 0.5) * 5;
        
        setTimeout(() => {
          const event = new MouseEvent('mousemove', {
            clientX: x,
            clientY: y,
            bubbles: true
          });
          document.dispatchEvent(event);
        }, i * 10);
      }
    }
  };
  
  // Override common automation detection functions
  const originalAddEventListener = EventTarget.prototype.addEventListener;
  EventTarget.prototype.addEventListener = function(type, listener, options) {
    // Block some automation detection event listeners
    if (typeof listener === 'function' && listener.toString().includes('webdriver')) {
      console.log('AI Automation Helper: Blocked automation detection listener');
      return;
    }
    return originalAddEventListener.call(this, type, listener, options);
  };
  
  // Mark as successfully injected
  window.__AUTOMATION_INJECTION_COMPLETE__ = true;
  
  console.log('AI Automation Helper: Injection complete, automation detection bypassed');
  
})();
