// Background service worker for AI automation helper
console.log('AI Automation Helper: Background script loaded');

// Enable automation-friendly settings
chrome.runtime.onInstalled.addListener(() => {
  console.log('AI Automation Helper: Extension installed');
  
  // Set up automation-friendly defaults
  chrome.storage.local.set({
    automationEnabled: true,
    allowAllPermissions: true,
    bypassSecurityWarnings: true
  });
});

// Handle messages from content scripts and external connections
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('AI Automation Helper: Message received', message);
  
  switch (message.type) {
    case 'AUTOMATION_READY':
      sendResponse({ status: 'ready', extensionId: chrome.runtime.id });
      break;
      
    case 'BYPASS_SECURITY':
      // Help bypass common security restrictions
      handleSecurityBypass(message.data, sendResponse);
      break;
      
    case 'INJECT_AUTOMATION_HELPERS':
      // Inject helper functions for automation
      injectAutomationHelpers(sender.tab.id, sendResponse);
      break;
      
    default:
      sendResponse({ status: 'unknown_message_type' });
  }
  
  return true; // Keep message channel open for async response
});

// Handle external connections (from Playwright/automation)
chrome.runtime.onConnectExternal.addListener((port) => {
  console.log('AI Automation Helper: External connection established');
  
  port.onMessage.addListener((message) => {
    console.log('AI Automation Helper: External message', message);
    
    switch (message.type) {
      case 'PING':
        port.postMessage({ type: 'PONG', timestamp: Date.now() });
        break;
        
      case 'ENABLE_AUTOMATION':
        enableAutomationMode(port);
        break;
        
      case 'GET_PAGE_INFO':
        getPageInfo(message.tabId, port);
        break;
    }
  });
});

// Function to handle security bypass requests
async function handleSecurityBypass(data, sendResponse) {
  try {
    // Disable web security warnings
    await chrome.storage.local.set({ securityBypassEnabled: true });
    
    sendResponse({ status: 'success', message: 'Security bypass enabled' });
  } catch (error) {
    console.error('AI Automation Helper: Security bypass failed', error);
    sendResponse({ status: 'error', message: error.message });
  }
}

// Function to inject automation helper functions
async function injectAutomationHelpers(tabId, sendResponse) {
  try {
    await chrome.scripting.executeScript({
      target: { tabId: tabId },
      files: ['injected.js']
    });
    
    sendResponse({ status: 'success', message: 'Automation helpers injected' });
  } catch (error) {
    console.error('AI Automation Helper: Injection failed', error);
    sendResponse({ status: 'error', message: error.message });
  }
}

// Function to enable automation mode
async function enableAutomationMode(port) {
  try {
    // Get all tabs and enable automation helpers
    const tabs = await chrome.tabs.query({});
    
    for (const tab of tabs) {
      try {
        await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          func: () => {
            // Mark page as automation-ready
            window.__AUTOMATION_ENABLED__ = true;
            window.__AUTOMATION_EXTENSION_ID__ = chrome.runtime.id;
            
            // Disable common automation detection
            Object.defineProperty(navigator, 'webdriver', {
              get: () => undefined,
              configurable: true
            });
            
            // Override automation detection methods
            window.chrome = window.chrome || {};
            window.chrome.runtime = window.chrome.runtime || {};
          }
        });
      } catch (e) {
        // Ignore errors for tabs we can't access
        console.log('AI Automation Helper: Could not access tab', tab.id);
      }
    }
    
    port.postMessage({ 
      type: 'AUTOMATION_ENABLED', 
      tabCount: tabs.length,
      timestamp: Date.now() 
    });
  } catch (error) {
    console.error('AI Automation Helper: Enable automation failed', error);
    port.postMessage({ 
      type: 'ERROR', 
      message: error.message 
    });
  }
}

// Function to get page information
async function getPageInfo(tabId, port) {
  try {
    const tab = await chrome.tabs.get(tabId);
    
    port.postMessage({
      type: 'PAGE_INFO',
      data: {
        url: tab.url,
        title: tab.title,
        status: tab.status,
        id: tab.id
      }
    });
  } catch (error) {
    console.error('AI Automation Helper: Get page info failed', error);
    port.postMessage({ 
      type: 'ERROR', 
      message: error.message 
    });
  }
}

// Listen for tab updates to maintain automation state
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete') {
    // Re-inject automation helpers on page load
    chrome.scripting.executeScript({
      target: { tabId: tabId },
      func: () => {
        window.__AUTOMATION_ENABLED__ = true;
        
        // Disable automation detection
        Object.defineProperty(navigator, 'webdriver', {
          get: () => undefined,
          configurable: true
        });
      }
    }).catch(() => {
      // Ignore errors for restricted pages
    });
  }
});
