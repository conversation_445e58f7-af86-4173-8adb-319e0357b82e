{"manifest_version": 3, "name": "Agentic AI Browser Automation Helper", "version": "1.0.0", "description": "Chrome extension to enable smooth AI browser automation", "permissions": ["activeTab", "tabs", "storage", "scripting", "webNavigation", "debugger"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js", "type": "module"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_start", "all_frames": true}], "action": {"default_popup": "popup.html", "default_title": "AI Automation Helper"}, "web_accessible_resources": [{"resources": ["injected.js"], "matches": ["<all_urls>"]}], "externally_connectable": {"matches": ["<all_urls>"]}}