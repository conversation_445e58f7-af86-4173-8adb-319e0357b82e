// Content script for AI automation helper
console.log('AI Automation Helper: Content script loaded');

// Mark page as automation-ready
window.__AUTOMATION_ENABLED__ = true;

// Disable common automation detection methods
(function() {
  'use strict';
  
  // Override webdriver property
  Object.defineProperty(navigator, 'webdriver', {
    get: () => undefined,
    configurable: true
  });
  
  // Override plugins detection
  Object.defineProperty(navigator, 'plugins', {
    get: () => [1, 2, 3, 4, 5], // Fake plugins array
    configurable: true
  });
  
  // Override languages
  Object.defineProperty(navigator, 'languages', {
    get: () => ['en-US', 'en'],
    configurable: true
  });
  
  // Override permissions
  const originalQuery = window.navigator.permissions.query;
  window.navigator.permissions.query = (parameters) => (
    parameters.name === 'notifications' ?
      Promise.resolve({ state: Notification.permission }) :
      originalQuery(parameters)
  );
  
  // Disable automation detection via chrome object
  window.chrome = window.chrome || {};
  window.chrome.runtime = window.chrome.runtime || {};
  
  // Override common automation detection functions
  const originalEval = window.eval;
  window.eval = function(code) {
    // Block common automation detection scripts
    if (typeof code === 'string' && (
      code.includes('webdriver') ||
      code.includes('__webdriver_script_fn') ||
      code.includes('selenium') ||
      code.includes('phantomjs')
    )) {
      console.log('AI Automation Helper: Blocked automation detection script');
      return undefined;
    }
    return originalEval.apply(this, arguments);
  };
  
  // Override Function constructor to block detection
  const originalFunction = window.Function;
  window.Function = function() {
    const code = arguments[arguments.length - 1];
    if (typeof code === 'string' && (
      code.includes('webdriver') ||
      code.includes('selenium') ||
      code.includes('phantomjs')
    )) {
      console.log('AI Automation Helper: Blocked Function-based detection');
      return function() { return undefined; };
    }
    return originalFunction.apply(this, arguments);
  };
  
  // Add automation helper functions to window
  window.__automationHelper__ = {
    // Helper to find elements more reliably
    findElement: function(selector, options = {}) {
      const { timeout = 5000, visible = true } = options;
      
      return new Promise((resolve, reject) => {
        const startTime = Date.now();
        
        function check() {
          const element = document.querySelector(selector);
          
          if (element) {
            if (!visible || isElementVisible(element)) {
              resolve(element);
              return;
            }
          }
          
          if (Date.now() - startTime > timeout) {
            reject(new Error(`Element not found: ${selector}`));
            return;
          }
          
          setTimeout(check, 100);
        }
        
        check();
      });
    },
    
    // Helper to check if element is visible
    isVisible: isElementVisible,
    
    // Helper to simulate human-like clicks
    humanClick: function(element) {
      const rect = element.getBoundingClientRect();
      const x = rect.left + rect.width / 2;
      const y = rect.top + rect.height / 2;
      
      // Dispatch mouse events in sequence
      ['mousedown', 'mouseup', 'click'].forEach(eventType => {
        const event = new MouseEvent(eventType, {
          view: window,
          bubbles: true,
          cancelable: true,
          clientX: x,
          clientY: y
        });
        element.dispatchEvent(event);
      });
    },
    
    // Helper to simulate human-like typing
    humanType: function(element, text, options = {}) {
      const { delay = 50 } = options;
      
      element.focus();
      element.value = '';
      
      return new Promise((resolve) => {
        let i = 0;
        
        function typeChar() {
          if (i < text.length) {
            element.value += text[i];
            
            // Dispatch input event
            element.dispatchEvent(new Event('input', { bubbles: true }));
            
            i++;
            setTimeout(typeChar, delay + Math.random() * 50);
          } else {
            // Dispatch change event
            element.dispatchEvent(new Event('change', { bubbles: true }));
            resolve();
          }
        }
        
        typeChar();
      });
    },
    
    // Helper to wait for page load
    waitForLoad: function(timeout = 10000) {
      return new Promise((resolve, reject) => {
        if (document.readyState === 'complete') {
          resolve();
          return;
        }
        
        const timer = setTimeout(() => {
          reject(new Error('Page load timeout'));
        }, timeout);
        
        window.addEventListener('load', () => {
          clearTimeout(timer);
          resolve();
        });
      });
    }
  };
  
  // Helper function to check element visibility
  function isElementVisible(element) {
    if (!element) return false;
    
    const style = window.getComputedStyle(element);
    const rect = element.getBoundingClientRect();
    
    return (
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0' &&
      rect.width > 0 &&
      rect.height > 0 &&
      rect.top >= 0 &&
      rect.left >= 0
    );
  }
  
})();

// Listen for messages from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('AI Automation Helper: Content script message', message);
  
  switch (message.type) {
    case 'CHECK_AUTOMATION_STATUS':
      sendResponse({ 
        status: 'ready', 
        automationEnabled: window.__AUTOMATION_ENABLED__,
        helpers: !!window.__automationHelper__
      });
      break;
      
    case 'EXECUTE_AUTOMATION_TASK':
      executeAutomationTask(message.data, sendResponse);
      break;
      
    default:
      sendResponse({ status: 'unknown_message_type' });
  }
  
  return true;
});

// Function to execute automation tasks
async function executeAutomationTask(data, sendResponse) {
  try {
    const { action, selector, value, options } = data;
    
    switch (action) {
      case 'click':
        const clickElement = await window.__automationHelper__.findElement(selector, options);
        window.__automationHelper__.humanClick(clickElement);
        sendResponse({ status: 'success', message: 'Click executed' });
        break;
        
      case 'type':
        const typeElement = await window.__automationHelper__.findElement(selector, options);
        await window.__automationHelper__.humanType(typeElement, value, options);
        sendResponse({ status: 'success', message: 'Typing executed' });
        break;
        
      case 'wait':
        await window.__automationHelper__.waitForLoad(options?.timeout);
        sendResponse({ status: 'success', message: 'Wait completed' });
        break;
        
      default:
        sendResponse({ status: 'error', message: 'Unknown action' });
    }
  } catch (error) {
    console.error('AI Automation Helper: Task execution failed', error);
    sendResponse({ status: 'error', message: error.message });
  }
}

// Notify background script that content script is ready
chrome.runtime.sendMessage({ type: 'AUTOMATION_READY' }, (response) => {
  console.log('AI Automation Helper: Ready notification sent', response);
});

console.log('AI Automation Helper: Content script initialization complete');
